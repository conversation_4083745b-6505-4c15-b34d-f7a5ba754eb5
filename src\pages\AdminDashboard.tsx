import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Switch } from "@/components/ui/switch";
import { mockArticles } from "@/data/mockData";
import { Category } from "@/types";
import { 
  BarChart3, 
  PenBox, 
  Trash2, 
  Users, 
  Settings, 
  Bell, 
  Lock, 
  Image, 
  Upload,
  FileText,
  UserPlus,
  Search,
  Filter,
  Eye
} from "lucide-react";
import { Link } from "react-router-dom";

export default function AdminDashboard() {
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [category, setCategory] = useState<Category>("Politics");
  const [isBreaking, setIsBreaking] = useState(false);
  const [isFeatured, setIsFeatured] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real application, this would be an API call to create/update an article
    console.log("Article submitted:", { title, content, category, isBreaking, isFeatured });
    // Reset form
    setTitle("");
    setContent("");
    setCategory("Politics");
    setIsBreaking(false);
    setIsFeatured(false);
  };

  // Mock users for user management tab
  const mockUsers = [
    { id: 1, name: "John Doe", email: "<EMAIL>", role: "Admin", status: "Active", lastLogin: "2023-07-01" },
    { id: 2, name: "Jane Smith", email: "<EMAIL>", role: "Editor", status: "Active", lastLogin: "2023-06-30" },
    { id: 3, name: "Michael Brown", email: "<EMAIL>", role: "Writer", status: "Inactive", lastLogin: "2023-06-25" },
    { id: 4, name: "Sarah Wilson", email: "<EMAIL>", role: "Subscriber", status: "Active", lastLogin: "2023-07-02" }
  ];
  
  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-[#121212] text-white py-4 px-6 flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Link to="/" className="text-xl font-bold text-[#D32F2F]">THE SACH PATRA</Link>
          <span className="text-sm">Admin Dashboard</span>
        </div>
        <div className="flex items-center space-x-4">
          <Bell className="h-5 w-5 cursor-pointer hover:text-[#D32F2F]" />
          <Button variant="outline">Logout</Button>
        </div>
      </header>
      
      <div className="container mx-auto px-4 py-6">
        <Tabs defaultValue="create" className="w-full">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="create">Create Article</TabsTrigger>
            <TabsTrigger value="manage">Manage Articles</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="users">User Management</TabsTrigger>
            <TabsTrigger value="settings">Website Settings</TabsTrigger>
            <TabsTrigger value="newsletter">Newsletter</TabsTrigger>
          </TabsList>
          
          {/* Create Article Tab */}
          <TabsContent value="create" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Create New Article</CardTitle>
                <CardDescription>
                  Fill in the details to create a new article.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <label htmlFor="title" className="block text-sm font-medium mb-1">
                      Article Title
                    </label>
                    <Input
                      id="title"
                      value={title}
                      onChange={(e) => setTitle(e.target.value)}
                      placeholder="Enter article title"
                      required
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="category" className="block text-sm font-medium mb-1">
                      Category
                    </label>
                    <Select 
                      value={category} 
                      onValueChange={(value) => setCategory(value as Category)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Politics">Politics</SelectItem>
                        <SelectItem value="Tech">Tech</SelectItem>
                        <SelectItem value="World">World</SelectItem>
                        <SelectItem value="Sports">Sports</SelectItem>
                        <SelectItem value="Entertainment">Entertainment</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <label htmlFor="content" className="block text-sm font-medium mb-1">
                      Article Content
                    </label>
                    <Textarea
                      id="content"
                      value={content}
                      onChange={(e) => setContent(e.target.value)}
                      placeholder="Enter article content"
                      rows={10}
                      required
                    />
                  </div>
                  
                  <div className="flex space-x-4">
                    <label className="flex items-center space-x-2">
                      <input 
                        type="checkbox" 
                        checked={isBreaking}
                        onChange={(e) => setIsBreaking(e.target.checked)}
                        className="rounded border-gray-300 text-[#D32F2F] focus:ring-[#D32F2F]"
                      />
                      <span>Breaking News</span>
                    </label>
                    
                    <label className="flex items-center space-x-2">
                      <input 
                        type="checkbox" 
                        checked={isFeatured}
                        onChange={(e) => setIsFeatured(e.target.checked)}
                        className="rounded border-gray-300 text-[#D32F2F] focus:ring-[#D32F2F]"
                      />
                      <span>Featured Article</span>
                    </label>
                  </div>
                  
                  <div>
                    <label htmlFor="image" className="block text-sm font-medium mb-1">
                      Featured Image
                    </label>
                    <Input id="image" type="file" />
                  </div>
                  
                  <Button type="submit" className="bg-[#D32F2F] hover:bg-red-700">
                    Create Article
                  </Button>
                </form>
              </CardContent>
            </Card>
          </TabsContent>
          
          {/* Manage Articles Tab */}
          <TabsContent value="manage">
            <Card>
              <CardHeader>
                <CardTitle>Manage Articles</CardTitle>
                <CardDescription>
                  View, edit, or delete existing articles.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Title</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {mockArticles.slice(0, 5).map((article) => (
                      <TableRow key={article.id}>
                        <TableCell className="font-medium">{article.title.substring(0, 30)}...</TableCell>
                        <TableCell>{article.category}</TableCell>
                        <TableCell>{new Date(article.publishedAt).toLocaleDateString()}</TableCell>
                        <TableCell>
                          {article.isBreaking ? 
                            <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">Breaking</span> : 
                            article.isFeatured ? 
                              <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">Featured</span> : 
                              <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">Published</span>
                          }
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button size="sm" variant="outline">
                              <PenBox size={16} />
                            </Button>
                            <Button size="sm" variant="outline" className="text-[#D32F2F]">
                              <Trash2 size={16} />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>
          
          {/* Analytics Tab */}
          <TabsContent value="analytics">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Article Views</CardTitle>
                </CardHeader>
                <CardContent className="flex flex-col items-center">
                  <BarChart3 size={64} className="text-[#1976D2] mb-4" />
                  <div className="text-4xl font-bold">1,243</div>
                  <p className="text-sm text-gray-500">Total views this week</p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Popular Categories</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div>
                      <div className="flex justify-between mb-1">
                        <span>Politics</span>
                        <span>42%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-[#D32F2F] h-2 rounded-full" style={{ width: "42%" }}></div>
                      </div>
                    </div>
                    <div>
                      <div className="flex justify-between mb-1">
                        <span>Tech</span>
                        <span>28%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-[#1976D2] h-2 rounded-full" style={{ width: "28%" }}></div>
                      </div>
                    </div>
                    <div>
                      <div className="flex justify-between mb-1">
                        <span>World</span>
                        <span>15%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-green-600 h-2 rounded-full" style={{ width: "15%" }}></div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>User Engagement</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <div className="text-2xl font-bold">124</div>
                      <p className="text-sm text-gray-500">New Comments</p>
                    </div>
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <div className="text-2xl font-bold">52</div>
                      <p className="text-sm text-gray-500">Shares</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Traffic Sources</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div>
                      <div className="flex justify-between mb-1">
                        <span>Social Media</span>
                        <span>38%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-purple-600 h-2 rounded-full" style={{ width: "38%" }}></div>
                      </div>
                    </div>
                    <div>
                      <div className="flex justify-between mb-1">
                        <span>Direct</span>
                        <span>32%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-orange-500 h-2 rounded-full" style={{ width: "32%" }}></div>
                      </div>
                    </div>
                    <div>
                      <div className="flex justify-between mb-1">
                        <span>Search</span>
                        <span>24%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-teal-500 h-2 rounded-full" style={{ width: "24%" }}></div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Device Statistics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div>
                      <div className="flex justify-between mb-1">
                        <span>Mobile</span>
                        <span>65%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-blue-500 h-2 rounded-full" style={{ width: "65%" }}></div>
                      </div>
                    </div>
                    <div>
                      <div className="flex justify-between mb-1">
                        <span>Desktop</span>
                        <span>30%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-green-500 h-2 rounded-full" style={{ width: "30%" }}></div>
                      </div>
                    </div>
                    <div>
                      <div className="flex justify-between mb-1">
                        <span>Tablet</span>
                        <span>5%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-yellow-500 h-2 rounded-full" style={{ width: "5%" }}></div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Popular Content</CardTitle>
                </CardHeader>
                <CardContent className="h-[280px] overflow-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Title</TableHead>
                        <TableHead>Views</TableHead>
                        <TableHead>CTR</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {mockArticles.slice(0, 5).map((article) => (
                        <TableRow key={article.id}>
                          <TableCell className="font-medium">{article.title.substring(0, 20)}...</TableCell>
                          <TableCell>{article.views || Math.floor(Math.random() * 1000)}</TableCell>
                          <TableCell>{(Math.random() * 10).toFixed(1)}%</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          {/* User Management Tab */}
          <TabsContent value="users">
            <div className="space-y-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-2xl font-bold">User Management</CardTitle>
                  <Button className="bg-[#D32F2F] hover:bg-red-700">
                    <UserPlus className="mr-2 h-4 w-4" /> Add User
                  </Button>
                </CardHeader>
                <CardContent>
                  <div className="mb-4 flex justify-between">
                    <div className="relative">
                      <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                      <Input
                        className="w-[300px] pl-9"
                        type="search"
                        placeholder="Search users..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                    </div>
                    <div className="flex items-center space-x-2">
                      <Filter className="h-4 w-4 text-gray-500" />
                      <Select defaultValue="all">
                        <SelectTrigger className="w-[150px]">
                          <SelectValue placeholder="Select role" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Roles</SelectItem>
                          <SelectItem value="admin">Admin</SelectItem>
                          <SelectItem value="editor">Editor</SelectItem>
                          <SelectItem value="writer">Writer</SelectItem>
                          <SelectItem value="subscriber">Subscriber</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Email</TableHead>
                        <TableHead>Role</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Last Login</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {mockUsers.map((user) => (
                        <TableRow key={user.id}>
                          <TableCell className="font-medium">{user.name}</TableCell>
                          <TableCell>{user.email}</TableCell>
                          <TableCell>
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              user.role === 'Admin' 
                                ? 'bg-purple-100 text-purple-800'
                                : user.role === 'Editor'
                                ? 'bg-blue-100 text-blue-800'
                                : user.role === 'Writer'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              {user.role}
                            </span>
                          </TableCell>
                          <TableCell>
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              user.status === 'Active' 
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {user.status}
                            </span>
                          </TableCell>
                          <TableCell>{user.lastLogin}</TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button size="sm" variant="outline">
                                <PenBox size={16} />
                              </Button>
                              <Button size="sm" variant="outline" className="text-[#D32F2F]">
                                <Trash2 size={16} />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Permission Settings</CardTitle>
                  <CardDescription>Define what users can do based on their roles</CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Permission</TableHead>
                        <TableHead>Admin</TableHead>
                        <TableHead>Editor</TableHead>
                        <TableHead>Writer</TableHead>
                        <TableHead>Subscriber</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      <TableRow>
                        <TableCell>Create Articles</TableCell>
                        <TableCell><Switch defaultChecked /></TableCell>
                        <TableCell><Switch defaultChecked /></TableCell>
                        <TableCell><Switch defaultChecked /></TableCell>
                        <TableCell><Switch /></TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Edit Any Article</TableCell>
                        <TableCell><Switch defaultChecked /></TableCell>
                        <TableCell><Switch defaultChecked /></TableCell>
                        <TableCell><Switch /></TableCell>
                        <TableCell><Switch /></TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Delete Articles</TableCell>
                        <TableCell><Switch defaultChecked /></TableCell>
                        <TableCell><Switch /></TableCell>
                        <TableCell><Switch /></TableCell>
                        <TableCell><Switch /></TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Manage Users</TableCell>
                        <TableCell><Switch defaultChecked /></TableCell>
                        <TableCell><Switch /></TableCell>
                        <TableCell><Switch /></TableCell>
                        <TableCell><Switch /></TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Change Site Settings</TableCell>
                        <TableCell><Switch defaultChecked /></TableCell>
                        <TableCell><Switch /></TableCell>
                        <TableCell><Switch /></TableCell>
                        <TableCell><Switch /></TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          {/* Website Settings Tab */}
          <TabsContent value="settings">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Settings className="mr-2 h-5 w-5" /> General Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label htmlFor="site-title" className="block text-sm font-medium mb-1">Site Title</label>
                    <Input id="site-title" defaultValue="The Sach Patra" />
                  </div>
                  
                  <div>
                    <label htmlFor="site-tagline" className="block text-sm font-medium mb-1">Tagline</label>
                    <Input id="site-tagline" defaultValue="Your trusted source for news" />
                  </div>
                  
                  <div>
                    <label htmlFor="site-description" className="block text-sm font-medium mb-1">Site Description</label>
                    <Textarea 
                      id="site-description" 
                      defaultValue="The Sach Patra - Your trusted source for the latest news on politics, tech, world, sports, and entertainment."
                      rows={3}
                    />
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Switch id="maintenance-mode" />
                    <label htmlFor="maintenance-mode">Maintenance Mode</label>
                  </div>

                  <Button className="bg-[#D32F2F] hover:bg-red-700 mt-4">Save Settings</Button>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Image className="mr-2 h-5 w-5" /> Appearance
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label htmlFor="logo" className="block text-sm font-medium mb-1">Site Logo</label>
                    <div className="flex items-center space-x-4">
                      <div className="h-16 w-16 bg-gray-200 rounded flex items-center justify-center">
                        <span className="text-gray-500">Logo</span>
                      </div>
                      <div>
                        <Input id="logo" type="file" />
                        <p className="text-xs text-gray-500 mt-1">Recommended size: 200x50px</p>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label htmlFor="favicon" className="block text-sm font-medium mb-1">Favicon</label>
                    <div className="flex items-center space-x-4">
                      <div className="h-8 w-8 bg-gray-200 rounded flex items-center justify-center">
                        <span className="text-gray-500 text-xs">Icon</span>
                      </div>
                      <div>
                        <Input id="favicon" type="file" />
                        <p className="text-xs text-gray-500 mt-1">Recommended size: 32x32px</p>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-2">Color Scheme</label>
                    <div className="grid grid-cols-4 gap-2">
                      <div className="flex flex-col items-center">
                        <div className="h-8 w-8 bg-[#D32F2F] rounded-full mb-1"></div>
                        <span className="text-xs">Primary</span>
                      </div>
                      <div className="flex flex-col items-center">
                        <div className="h-8 w-8 bg-[#1976D2] rounded-full mb-1"></div>
                        <span className="text-xs">Secondary</span>
                      </div>
                      <div className="flex flex-col items-center">
                        <div className="h-8 w-8 bg-[#121212] rounded-full mb-1"></div>
                        <span className="text-xs">Text</span>
                      </div>
                      <div className="flex flex-col items-center">
                        <div className="h-8 w-8 bg-white border border-gray-200 rounded-full mb-1"></div>
                        <span className="text-xs">Background</span>
                      </div>
                    </div>
                  </div>

                  <Button className="bg-[#D32F2F] hover:bg-red-700 mt-4">Save Appearance</Button>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <FileText className="mr-2 h-5 w-5" /> Content Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label htmlFor="posts-per-page" className="block text-sm font-medium mb-1">Articles Per Page</label>
                    <Select defaultValue="10">
                      <SelectTrigger>
                        <SelectValue placeholder="Select number" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="5">5</SelectItem>
                        <SelectItem value="10">10</SelectItem>
                        <SelectItem value="15">15</SelectItem>
                        <SelectItem value="20">20</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-1">Featured Categories</label>
                    <div className="grid grid-cols-2 gap-2">
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" id="cat-politics" defaultChecked />
                        <label htmlFor="cat-politics">Politics</label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" id="cat-tech" defaultChecked />
                        <label htmlFor="cat-tech">Tech</label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" id="cat-world" defaultChecked />
                        <label htmlFor="cat-world">World</label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" id="cat-sports" defaultChecked />
                        <label htmlFor="cat-sports">Sports</label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" id="cat-entertainment" defaultChecked />
                        <label htmlFor="cat-entertainment">Entertainment</label>
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col space-y-2">
                    <div className="flex items-center justify-between">
                      <label htmlFor="comments-enabled" className="text-sm font-medium">Enable Comments</label>
                      <Switch id="comments-enabled" defaultChecked />
                    </div>
                    <div className="flex items-center justify-between">
                      <label htmlFor="moderate-comments" className="text-sm font-medium">Moderate Comments</label>
                      <Switch id="moderate-comments" defaultChecked />
                    </div>
                  </div>

                  <Button className="bg-[#D32F2F] hover:bg-red-700 mt-4">Save Content Settings</Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Lock className="mr-2 h-5 w-5" /> Security Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex flex-col space-y-2">
                    <div className="flex items-center justify-between">
                      <label htmlFor="two-factor" className="text-sm font-medium">Two-Factor Authentication</label>
                      <Switch id="two-factor" />
                    </div>
                    <div className="flex items-center justify-between">
                      <label htmlFor="captcha" className="text-sm font-medium">CAPTCHA on Forms</label>
                      <Switch id="captcha" defaultChecked />
                    </div>
                    <div className="flex items-center justify-between">
                      <label htmlFor="auto-logout" className="text-sm font-medium">Auto Logout (30 min inactive)</label>
                      <Switch id="auto-logout" defaultChecked />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="password-policy" className="block text-sm font-medium mb-1">Password Policy</label>
                    <Select defaultValue="strong">
                      <SelectTrigger>
                        <SelectValue placeholder="Select policy" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="basic">Basic (8+ characters)</SelectItem>
                        <SelectItem value="medium">Medium (8+ chars, numbers + letters)</SelectItem>
                        <SelectItem value="strong">Strong (8+ chars, numbers, letters, special)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <Button className="bg-[#D32F2F] hover:bg-red-700 mt-4">Save Security Settings</Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Newsletter Management Tab - New Addition */}
          <TabsContent value="newsletter">
            <div className="space-y-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <div>
                    <CardTitle className="text-2xl font-bold">Newsletter Management</CardTitle>
                    <CardDescription>Create and manage email newsletters</CardDescription>
                  </div>
                  <Button className="bg-[#D32F2F] hover:bg-red-700">
                    <Upload className="mr-2 h-4 w-4" /> Create Newsletter
                  </Button>
                </CardHeader>
                <CardContent>
                  <div className="mb-4">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                      <div>
                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-lg">Subscriber Statistics</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="flex flex-col space-y-4">
                              <div className="flex justify-between items-center">
                                <span>Total Subscribers</span>
                                <span className="font-bold">4,289</span>
                              </div>
                              <div className="flex justify-between items-center">
                                <span>Active Subscribers</span>
                                <span className="font-bold">3,975</span>
                              </div>
                              <div className="flex justify-between items-center">
                                <span>Unsubscribed</span>
                                <span className="font-bold">314</span>
                              </div>
                              <div className="flex justify-between items-center">
                                <span>Average Open Rate</span>
                                <span className="font-bold">38.2%</span>
                              </div>
                              <div className="flex justify-between items-center">
                                <span>Average Click Rate</span>
                                <span className="font-bold">12.7%</span>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                      
                      <div>
                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-lg">Recent Growth</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-2">
                              <div>
                                <div className="flex justify-between mb-1">
                                  <span>This Month</span>
                                  <span>+142</span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-2">
                                  <div className="bg-green-500 h-2 rounded-full" style={{ width: "65%" }}></div>
                                </div>
                              </div>
                              <div>
                                <div className="flex justify-between mb-1">
                                  <span>Last Month</span>
                                  <span>+98</span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-2">
                                  <div className="bg-green-500 h-2 rounded-full" style={{ width: "45%" }}></div>
                                </div>
                              </div>
                              <div>
                                <div className="flex justify-between mb-1">
                                  <span>3 Months Ago</span>
                                  <span>+78</span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-2">
                                  <div className="bg-green-500 h-2 rounded-full" style={{ width: "35%" }}></div>
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    </div>
                  </div>

                  <div className="mt-6">
                    <h3 className="text-lg font-medium mb-4">Recent Newsletters</h3>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Title</TableHead>
                          <TableHead>Date Sent</TableHead>
                          <TableHead>Recipients</TableHead>
                          <TableHead>Open Rate</TableHead>
                          <TableHead>Click Rate</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        <TableRow>
                          <TableCell className="font-medium">Weekly Digest: Top Politics Stories</TableCell>
                          <TableCell>July 1, 2023</TableCell>
                          <TableCell>3,845</TableCell>
                          <TableCell>42%</TableCell>
                          <TableCell>15%</TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button size="sm" variant="outline">
                                <Eye size={16} />
                              </Button>
                              <Button size="sm" variant="outline">
                                <PenBox size={16} />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell className="font-medium">Breaking News: Special Edition</TableCell>
                          <TableCell>June 28, 2023</TableCell>
                          <TableCell>3,912</TableCell>
                          <TableCell>58%</TableCell>
                          <TableCell>23%</TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button size="sm" variant="outline">
                                <Eye size={16} />
                              </Button>
                              <Button size="sm" variant="outline">
                                <PenBox size={16} />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell className="font-medium">Tech Innovations Monthly</TableCell>
                          <TableCell>June 15, 2023</TableCell>
                          <TableCell>2,756</TableCell>
                          <TableCell>36%</TableCell>
                          <TableCell>14%</TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button size="sm" variant="outline">
                                <Eye size={16} />
                              </Button>
                              <Button size="sm" variant="outline">
                                <PenBox size={16} />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </div>
                  
                  <div className="mt-6">
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-lg">Newsletter Template</CardTitle>
                        <CardDescription>Configure your newsletter template</CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div>
                          <label htmlFor="template-name" className="block text-sm font-medium mb-1">Template Name</label>
                          <Input id="template-name" defaultValue="Standard Newsletter Template" />
                        </div>
                        
                        <div>
                          <label htmlFor="template-subject" className="block text-sm font-medium mb-1">Default Subject Line</label>
                          <Input id="template-subject" defaultValue="[The Sach Patra] Weekly Newsletter" />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium mb-1">Include Sections</label>
                          <div className="grid grid-cols-2 gap-2">
                            <div className="flex items-center space-x-2">
                              <input type="checkbox" id="section-top-stories" defaultChecked />
                              <label htmlFor="section-top-stories">Top Stories</label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <input type="checkbox" id="section-breaking" defaultChecked />
                              <label htmlFor="section-breaking">Breaking News</label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <input type="checkbox" id="section-popular" defaultChecked />
                              <label htmlFor="section-popular">Most Popular</label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <input type="checkbox" id="section-editor" defaultChecked />
                              <label htmlFor="section-editor">Editor's Picks</label>
                            </div>
                          </div>
                        </div>
                        
                        <Button className="bg-[#D32F2F] hover:bg-red-700">Save Template</Button>
                      </CardContent>
                    </Card>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
