-- =====================================================
-- ADMIN PANEL SQL QUERIES FOR THE SACH PATRA
-- =====================================================

-- =====================================================
-- ARTICLE ANALYTICS QUERIES
-- =====================================================

-- Get total article views this week
SELECT SUM(views) as total_views_this_week
FROM articles 
WHERE published_at >= NOW() - INTERVAL '7 days';

-- Get most popular articles by views
SELECT 
    id,
    title,
    views,
    category,
    published_at,
    (SELECT full_name FROM user_profiles WHERE id = articles.author_id) as author_name
FROM articles 
ORDER BY views DESC 
LIMIT 10;

-- Get article count by category
SELECT 
    category,
    COUNT(*) as article_count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM articles), 2) as percentage
FROM articles 
GROUP BY category 
ORDER BY article_count DESC;

-- Get articles published this month
SELECT 
    COUNT(*) as articles_this_month,
    COUNT(CASE WHEN is_featured = true THEN 1 END) as featured_articles,
    COUNT(CASE WHEN is_breaking = true THEN 1 END) as breaking_articles
FROM articles 
WHERE published_at >= DATE_TRUNC('month', NOW());

-- Get top performing articles by engagement (views + comments)
SELECT 
    a.id,
    a.title,
    a.views,
    COUNT(c.id) as comment_count,
    (a.views + COUNT(c.id) * 10) as engagement_score
FROM articles a
LEFT JOIN comments c ON a.id = c.article_id
GROUP BY a.id, a.title, a.views
ORDER BY engagement_score DESC
LIMIT 10;

-- =====================================================
-- USER ANALYTICS QUERIES
-- =====================================================

-- Get user statistics by role
SELECT 
    role,
    COUNT(*) as user_count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM user_profiles), 2) as percentage
FROM user_profiles 
GROUP BY role 
ORDER BY user_count DESC;

-- Get most active users (by comments)
SELECT 
    up.id,
    up.username,
    up.full_name,
    up.role,
    COUNT(c.id) as comment_count
FROM user_profiles up
LEFT JOIN comments c ON up.id = c.user_id
GROUP BY up.id, up.username, up.full_name, up.role
ORDER BY comment_count DESC
LIMIT 10;

-- Get user registration trends (last 6 months)
SELECT 
    DATE_TRUNC('month', created_at) as month,
    COUNT(*) as new_users
FROM user_profiles 
WHERE created_at >= NOW() - INTERVAL '6 months'
GROUP BY DATE_TRUNC('month', created_at)
ORDER BY month;

-- =====================================================
-- ADVERTISEMENT ANALYTICS QUERIES
-- =====================================================

-- Get advertisement performance summary
SELECT 
    COUNT(*) as total_ads,
    COUNT(CASE WHEN is_active = true THEN 1 END) as active_ads,
    SUM(clicks) as total_clicks,
    SUM(impressions) as total_impressions,
    CASE 
        WHEN SUM(impressions) > 0 THEN ROUND(SUM(clicks) * 100.0 / SUM(impressions), 2)
        ELSE 0 
    END as overall_ctr
FROM advertisements;

-- Get top performing advertisements
SELECT 
    id,
    title,
    position,
    clicks,
    impressions,
    CASE 
        WHEN impressions > 0 THEN ROUND(clicks * 100.0 / impressions, 2)
        ELSE 0 
    END as ctr,
    is_active
FROM advertisements 
ORDER BY clicks DESC
LIMIT 10;

-- Get advertisement performance by position
SELECT 
    position,
    COUNT(*) as ad_count,
    SUM(clicks) as total_clicks,
    SUM(impressions) as total_impressions,
    CASE 
        WHEN SUM(impressions) > 0 THEN ROUND(SUM(clicks) * 100.0 / SUM(impressions), 2)
        ELSE 0 
    END as avg_ctr
FROM advertisements 
GROUP BY position 
ORDER BY total_clicks DESC;

-- =====================================================
-- NEWSLETTER ANALYTICS QUERIES
-- =====================================================

-- Get newsletter subscriber statistics
SELECT 
    COUNT(*) as total_subscribers,
    COUNT(CASE WHEN is_active = true THEN 1 END) as active_subscribers,
    COUNT(CASE WHEN is_active = false THEN 1 END) as unsubscribed,
    ROUND(COUNT(CASE WHEN is_active = true THEN 1 END) * 100.0 / COUNT(*), 2) as retention_rate
FROM newsletter_subscribers;

-- Get newsletter growth trends (last 6 months)
SELECT 
    DATE_TRUNC('month', created_at) as month,
    COUNT(*) as new_subscribers
FROM newsletter_subscribers 
WHERE created_at >= NOW() - INTERVAL '6 months'
GROUP BY DATE_TRUNC('month', created_at)
ORDER BY month;

-- Get newsletter campaign performance
SELECT 
    id,
    title,
    subject,
    recipients_count,
    opened_count,
    clicked_count,
    CASE 
        WHEN recipients_count > 0 THEN ROUND(opened_count * 100.0 / recipients_count, 2)
        ELSE 0 
    END as open_rate,
    CASE 
        WHEN opened_count > 0 THEN ROUND(clicked_count * 100.0 / opened_count, 2)
        ELSE 0 
    END as click_rate,
    sent_at
FROM newsletter_campaigns 
WHERE sent_at IS NOT NULL
ORDER BY sent_at DESC;

-- =====================================================
-- CONTENT MANAGEMENT QUERIES
-- =====================================================

-- Get articles needing moderation (if you implement a moderation system)
SELECT 
    a.id,
    a.title,
    a.category,
    a.published_at,
    up.full_name as author_name,
    COUNT(c.id) as comment_count
FROM articles a
JOIN user_profiles up ON a.author_id = up.id
LEFT JOIN comments c ON a.id = c.article_id
WHERE a.published_at > NOW() - INTERVAL '24 hours'
GROUP BY a.id, a.title, a.category, a.published_at, up.full_name
ORDER BY a.published_at DESC;

-- Get most commented articles
SELECT 
    a.id,
    a.title,
    a.category,
    COUNT(c.id) as comment_count,
    a.published_at
FROM articles a
LEFT JOIN comments c ON a.id = c.article_id
GROUP BY a.id, a.title, a.category, a.published_at
ORDER BY comment_count DESC
LIMIT 10;

-- =====================================================
-- WEBSITE SETTINGS QUERIES
-- =====================================================

-- Get all website settings
SELECT 
    setting_key,
    setting_value,
    setting_type,
    description,
    updated_at
FROM website_settings 
ORDER BY setting_key;

-- Get specific setting value
SELECT setting_value 
FROM website_settings 
WHERE setting_key = 'site_title';

-- =====================================================
-- DASHBOARD OVERVIEW QUERIES
-- =====================================================

-- Get dashboard overview statistics
SELECT 
    (SELECT COUNT(*) FROM articles) as total_articles,
    (SELECT COUNT(*) FROM articles WHERE published_at >= NOW() - INTERVAL '7 days') as articles_this_week,
    (SELECT COUNT(*) FROM user_profiles) as total_users,
    (SELECT COUNT(*) FROM user_profiles WHERE created_at >= NOW() - INTERVAL '7 days') as new_users_this_week,
    (SELECT COUNT(*) FROM comments WHERE created_at >= NOW() - INTERVAL '7 days') as comments_this_week,
    (SELECT COUNT(*) FROM newsletter_subscribers WHERE is_active = true) as active_subscribers,
    (SELECT SUM(views) FROM articles) as total_article_views,
    (SELECT COUNT(*) FROM advertisements WHERE is_active = true) as active_advertisements;

-- Get recent activity summary
SELECT 
    'article' as activity_type,
    title as activity_title,
    published_at as activity_date,
    (SELECT full_name FROM user_profiles WHERE id = author_id) as user_name
FROM articles 
WHERE published_at >= NOW() - INTERVAL '7 days'

UNION ALL

SELECT 
    'comment' as activity_type,
    SUBSTRING(content, 1, 50) || '...' as activity_title,
    created_at as activity_date,
    (SELECT full_name FROM user_profiles WHERE id = user_id) as user_name
FROM comments 
WHERE created_at >= NOW() - INTERVAL '7 days'

UNION ALL

SELECT 
    'user' as activity_type,
    'New user: ' || full_name as activity_title,
    created_at as activity_date,
    full_name as user_name
FROM user_profiles 
WHERE created_at >= NOW() - INTERVAL '7 days'

ORDER BY activity_date DESC
LIMIT 20;

-- =====================================================
-- PERFORMANCE OPTIMIZATION QUERIES
-- =====================================================

-- Find articles with low engagement (low views relative to publish date)
SELECT 
    id,
    title,
    views,
    published_at,
    EXTRACT(DAYS FROM NOW() - published_at) as days_since_published,
    CASE 
        WHEN EXTRACT(DAYS FROM NOW() - published_at) > 0 
        THEN ROUND(views / EXTRACT(DAYS FROM NOW() - published_at), 2)
        ELSE views 
    END as views_per_day
FROM articles 
WHERE published_at <= NOW() - INTERVAL '1 day'
ORDER BY views_per_day ASC
LIMIT 10;

-- Find most engaging content by category
SELECT 
    category,
    AVG(views) as avg_views,
    AVG((SELECT COUNT(*) FROM comments WHERE article_id = articles.id)) as avg_comments,
    COUNT(*) as article_count
FROM articles 
GROUP BY category 
ORDER BY avg_views DESC;
