import { ReactNode } from "react";
import Header from "./Header";
import Footer from "./Footer";
import BreakingNewsTicker from "./BreakingNewsTicker";
import { mockArticles } from "@/data/mockData";

interface LayoutProps {
  children: ReactNode;
}

export default function Layout({ children }: LayoutProps) {
  // Filter breaking news articles
  const breakingNews = mockArticles.filter(article => article.isBreaking);

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <BreakingNewsTicker breakingNews={breakingNews} />
      <main className="flex-1">{children}</main>
      <Footer />
    </div>
  );
}