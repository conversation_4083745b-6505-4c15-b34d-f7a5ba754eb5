import { supabase } from './supabase';
import { Article, Comment, Category, Bookmark, NewsletterSubscriber } from '@/types';

// Article operations
export const articleService = {
  // Get all articles with pagination and filters
  async getArticles(options: {
    page?: number;
    limit?: number;
    category?: Category;
    featured?: boolean;
    breaking?: boolean;
    trending?: boolean;
    search?: string;
  } = {}) {
    const { page = 1, limit = 10, category, featured, breaking, trending, search } = options;
    const offset = (page - 1) * limit;

    let query = supabase
      .from('articles')
      .select(`
        *,
        author:user_profiles(id, username, full_name, avatar_url)
      `)
      .order('published_at', { ascending: false });

    if (category) {
      query = query.eq('category', category);
    }

    if (featured !== undefined) {
      query = query.eq('is_featured', featured);
    }

    if (breaking !== undefined) {
      query = query.eq('is_breaking', breaking);
    }

    if (trending !== undefined) {
      query = query.eq('is_trending', trending);
    }

    if (search) {
      query = query.or(`title.ilike.%${search}%,summary.ilike.%${search}%,content.ilike.%${search}%`);
    }

    const { data, error, count } = await query
      .range(offset, offset + limit - 1)
      .limit(limit);

    if (error) throw error;

    return {
      articles: data || [],
      totalCount: count || 0,
      hasMore: (count || 0) > offset + limit
    };
  },

  // Get single article by ID
  async getArticle(id: string) {
    const { data, error } = await supabase
      .from('articles')
      .select(`
        *,
        author:user_profiles(id, username, full_name, avatar_url),
        comments(
          *,
          user_profile:user_profiles(id, username, full_name, avatar_url)
        )
      `)
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  },

  // Create new article
  async createArticle(article: Omit<Article, 'id' | 'created_at' | 'updated_at' | 'views'>) {
    const { data, error } = await supabase
      .from('articles')
      .insert([article])
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Update article
  async updateArticle(id: string, updates: Partial<Article>) {
    const { data, error } = await supabase
      .from('articles')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Delete article
  async deleteArticle(id: string) {
    const { error } = await supabase
      .from('articles')
      .delete()
      .eq('id', id);

    if (error) throw error;
  },

  // Increment article views
  async incrementViews(id: string) {
    const { error } = await supabase.rpc('increment_article_views', {
      article_uuid: id
    });

    if (error) throw error;
  },

  // Get articles by category
  async getArticlesByCategory(category: Category, limit = 10) {
    const { data, error } = await supabase
      .from('articles')
      .select(`
        *,
        author:user_profiles(id, username, full_name, avatar_url)
      `)
      .eq('category', category)
      .order('published_at', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data || [];
  }
};

// Comment operations
export const commentService = {
  // Get comments for an article
  async getComments(articleId: string) {
    const { data, error } = await supabase
      .from('comments')
      .select(`
        *,
        user_profile:user_profiles(id, username, full_name, avatar_url)
      `)
      .eq('article_id', articleId)
      .order('created_at', { ascending: true });

    if (error) throw error;
    return data || [];
  },

  // Create new comment
  async createComment(comment: Omit<Comment, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('comments')
      .insert([comment])
      .select(`
        *,
        user_profile:user_profiles(id, username, full_name, avatar_url)
      `)
      .single();

    if (error) throw error;
    return data;
  },

  // Update comment
  async updateComment(id: string, content: string) {
    const { data, error } = await supabase
      .from('comments')
      .update({ content })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Delete comment
  async deleteComment(id: string) {
    const { error } = await supabase
      .from('comments')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }
};

// Bookmark operations
export const bookmarkService = {
  // Get user bookmarks
  async getUserBookmarks(userId: string) {
    const { data, error } = await supabase
      .from('bookmarks')
      .select(`
        *,
        article:articles(
          *,
          author:user_profiles(id, username, full_name, avatar_url)
        )
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  },

  // Check if article is bookmarked
  async isBookmarked(userId: string, articleId: string) {
    const { data, error } = await supabase
      .from('bookmarks')
      .select('id')
      .eq('user_id', userId)
      .eq('article_id', articleId)
      .single();

    if (error && error.code !== 'PGRST116') throw error;
    return !!data;
  },

  // Add bookmark
  async addBookmark(userId: string, articleId: string) {
    const { data, error } = await supabase
      .from('bookmarks')
      .insert([{ user_id: userId, article_id: articleId }])
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Remove bookmark
  async removeBookmark(userId: string, articleId: string) {
    const { error } = await supabase
      .from('bookmarks')
      .delete()
      .eq('user_id', userId)
      .eq('article_id', articleId);

    if (error) throw error;
  }
};

// Newsletter operations
export const newsletterService = {
  // Subscribe to newsletter
  async subscribe(email: string) {
    const { data, error } = await supabase
      .from('newsletter_subscribers')
      .insert([{ email }])
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Unsubscribe from newsletter
  async unsubscribe(email: string) {
    const { error } = await supabase
      .from('newsletter_subscribers')
      .update({ is_active: false })
      .eq('email', email);

    if (error) throw error;
  },

  // Get all subscribers (admin only)
  async getSubscribers() {
    const { data, error } = await supabase
      .from('newsletter_subscribers')
      .select('*')
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }
};
