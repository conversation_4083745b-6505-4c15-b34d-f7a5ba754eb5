export interface Article {
  id: string;
  title: string;
  summary: string;
  content: string;
  category: Category;
  image_url: string;
  author_id: string;
  published_at: string;
  is_featured: boolean;
  is_breaking: boolean;
  is_trending: boolean;
  views: number;
  created_at: string;
  updated_at: string;
  // Joined data
  author?: UserProfile;
  comments?: Comment[];
}

export type Category = 'Politics' | 'Tech' | 'World' | 'Sports' | 'Entertainment';

export interface Comment {
  id: string;
  article_id: string;
  user_id: string;
  content: string;
  created_at: string;
  updated_at: string;
  // Joined data
  user_profile?: UserProfile;
}

export interface UserProfile {
  id: string;
  username: string;
  full_name: string;
  avatar_url: string | null;
  role: 'user' | 'admin' | 'editor' | 'writer';
  created_at: string;
  updated_at: string;
}

export interface Bookmark {
  id: string;
  user_id: string;
  article_id: string;
  created_at: string;
  // Joined data
  article?: Article;
}

export interface NewsletterSubscriber {
  id: string;
  email: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}