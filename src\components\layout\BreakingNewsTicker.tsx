import { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { Article } from "@/types";
import { cn } from "@/lib/utils";

interface BreakingNewsTickerProps {
  breakingNews: Article[];
}

export default function BreakingNewsTicker({ breakingNews }: BreakingNewsTickerProps) {
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if (breakingNews.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % breakingNews.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [breakingNews.length]);

  if (breakingNews.length === 0) return null;

  return (
    <div className="bg-[#D32F2F] text-white py-2 overflow-hidden">
      <div className="container mx-auto px-4">
        <div className="flex items-center">
          <div className="mr-4 font-bold whitespace-nowrap">BREAKING:</div>
          <div className="w-full overflow-hidden">
            {breakingNews.map((news, index) => (
              <Link
                key={news.id}
                to={`/article/${news.id}`}
                className={cn(
                  "block whitespace-nowrap transition-all duration-500 transform",
                  index === currentIndex ? "translate-y-0 opacity-100" : "translate-y-8 opacity-0 absolute"
                )}
              >
                {news.title}
              </Link>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}