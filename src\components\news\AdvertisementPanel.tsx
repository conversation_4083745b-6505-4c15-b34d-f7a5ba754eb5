interface Advertisement {
  id: string;
  title: string;
  description: string;
  imageUrl: string;
  link: string;
}

interface AdvertisementPanelProps {
  advertisements: Advertisement[];
}

export default function AdvertisementPanel({ advertisements }: AdvertisementPanelProps) {
  return (
    <div className="space-y-4">
      <div className="text-xs text-gray-500 uppercase tracking-wider">Sponsored</div>
      {advertisements.map((ad) => (
        <a
          key={ad.id}
          href={ad.link}
          className="block bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow"
        >
          <img
            src={ad.imageUrl}
            alt={ad.title}
            className="w-full h-32 object-cover"
          />
          <div className="p-3">
            <h4 className="font-semibold text-sm">{ad.title}</h4>
            <p className="text-xs text-gray-500 mt-1">{ad.description}</p>
          </div>
        </a>
      ))}
    </div>
  );
}