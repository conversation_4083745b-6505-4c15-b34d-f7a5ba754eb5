import { Article } from "@/types";
import { Link } from "react-router-dom";
import { Badge } from "@/components/ui/badge";
import { CalendarIcon, Eye } from "lucide-react";
import { cn } from "@/lib/utils";

interface ArticleCardProps {
  article: Article;
  variant?: "default" | "featured" | "compact";
}

export default function ArticleCard({ 
  article, 
  variant = "default" 
}: ArticleCardProps) {
  const {
    id,
    title,
    summary,
    category,
    imageUrl,
    publishedAt,
    author,
    views,
    isBreaking,
    isTrending
  } = article;

  const date = new Date(publishedAt).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });

  return (
    <article 
      className={cn(
        "group overflow-hidden bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow",
        variant === "compact" && "flex h-24",
        variant === "featured" && "h-full"
      )}
    >
      <Link to={`/article/${id}`} className={cn(
        "block",
        variant === "compact" && "flex"
      )}>
        <div className={cn(
          "overflow-hidden",
          variant === "default" && "h-48",
          variant === "featured" && "h-56",
          variant === "compact" && "flex-shrink-0 w-24 h-24"
        )}>
          <img
            src={imageUrl}
            alt={title}
            className={cn(
              "h-full w-full object-cover transition-transform duration-300",
              variant !== "compact" && "group-hover:scale-105"
            )}
          />
        </div>
        <div className={cn(
          "p-4",
          variant === "compact" && "flex-1"
        )}>
          <div className="flex items-center space-x-2 mb-2">
            <Badge className="bg-[#1976D2] hover:bg-blue-700">{category}</Badge>
            {isBreaking && (
              <Badge className="bg-[#D32F2F] hover:bg-red-700">Breaking</Badge>
            )}
            {isTrending && (
              <Badge variant="outline" className="text-[#D32F2F] border-[#D32F2F]">
                Trending
              </Badge>
            )}
          </div>
          <h3 className={cn(
            "font-bold text-gray-900 group-hover:text-[#D32F2F] transition-colors",
            variant === "featured" && "text-xl mb-2",
            variant === "default" && "mb-2",
            variant === "compact" && "text-sm"
          )}>
            {title}
          </h3>
          {variant !== "compact" && (
            <p className="text-gray-600 text-sm mb-3 line-clamp-2">{summary}</p>
          )}
          <div className="flex justify-between items-center text-xs text-gray-500">
            <div className="flex items-center space-x-1">
              <CalendarIcon size={12} />
              <span>{date}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Eye size={12} />
              <span>{views}</span>
            </div>
          </div>
        </div>
      </Link>
    </article>
  );
}