import { Link } from "react-router-dom";
import Layout from "@/components/layout/Layout";
import { But<PERSON> } from "@/components/ui/button";

export default function NotFound() {
  return (
    <Layout>
      <div className="container mx-auto px-4 py-12 text-center">
        <div className="max-w-md mx-auto">
          <h1 className="text-9xl font-bold text-[#D32F2F]">404</h1>
          <h2 className="text-3xl font-bold mb-4">Page Not Found</h2>
          <p className="text-gray-600 mb-8">
            The page you are looking for doesn't exist or has been moved.
          </p>
          <Button asChild className="bg-[#D32F2F] hover:bg-red-700">
            <Link to="/">Back to Homepage</Link>
          </Button>
        </div>
      </div>
    </Layout>
  );
}