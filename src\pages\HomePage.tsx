import Layout from "@/components/layout/Layout";
import ArticleCarousel from "@/components/news/ArticleCarousel";
import CategoryTabs from "@/components/news/CategoryTabs";
import EditorsPicks from "@/components/news/EditorsPicks";
import AdvertisementPanel from "@/components/news/AdvertisementPanel";
import TrendingArticles from "@/components/news/TrendingArticles";
import { mockArticles, editorsPicksArticles, advertisements } from "@/data/mockData";
import { useFeaturedArticles, useTrendingArticles, useArticles } from "@/hooks/useArticles";
import { formatArticlesForDisplay } from "@/lib/articleUtils";
import { Article } from "@/types";

export default function HomePage() {
  // Try to fetch real data, fallback to mock data
  const { data: featuredData, isLoading: featuredLoading } = useFeaturedArticles(5);
  const { data: trendingData, isLoading: trendingLoading } = useTrendingArticles(5);
  const { data: allArticlesData, isLoading: allArticlesLoading } = useArticles({ limit: 20 });

  // Use real data if available, otherwise fallback to mock data
  const featuredArticles = featuredData?.articles?.length
    ? formatArticlesForDisplay(featuredData.articles)
    : mockArticles.filter((article) => article.isFeatured).slice(0, 5);

  const trendingArticles = trendingData?.articles?.length
    ? formatArticlesForDisplay(trendingData.articles)
    : mockArticles.filter((article) => article.isTrending).slice(0, 5);

  const allArticles = allArticlesData?.articles?.length
    ? formatArticlesForDisplay(allArticlesData.articles)
    : mockArticles;
  
  return (
    <Layout>
      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
          {/* Left Sidebar - Editor's Picks */}
          <div className="lg:col-span-3 order-3 lg:order-1">
            <EditorsPicks articles={editorsPicksArticles} />
          </div>
          
          {/* Main Content */}
          <div className="lg:col-span-6 space-y-8 order-1 lg:order-2">
            {/* Top Carousel */}
            <ArticleCarousel articles={featuredArticles} />
            
            {/* Category Tabs */}
            <div>
              <h2 className="text-2xl font-bold mb-4">News Categories</h2>
              <CategoryTabs articles={allArticles} />
            </div>
          </div>
          
          {/* Right Sidebar - Ads & Trending */}
          <div className="lg:col-span-3 space-y-6 order-2 lg:order-3">
            <AdvertisementPanel advertisements={advertisements} />
            <TrendingArticles articles={trendingArticles} />
          </div>
        </div>
      </div>
    </Layout>
  );
}